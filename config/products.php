<?php
/**
 * =======================================
 * ===== IMPORTANT - PLATFORM CONFIG =====
 * =======================================
 *
 * Please note, this config file is provided by platform!
 * Ensure all changes that apply to platform are made there.
 * Be careful publishing and merging updates from platform.
 */
return [
    'supported' => [
        // STARTER
        'aud-a-starter-1',
        'cad-a-starter-1',
        'eur-a-starter-1',
        'gbp-a-starter-1',
        'hkd-a-starter-1',
        'usd-m-starter-1',
        'usd-a-starter-1',
        // PLUS
        'aud-a-plus-1',
        'cad-a-plus-1',
        'eur-a-plus-1',
        'gbp-a-plus-1',
        'hkd-a-plus-1',
        'usd-m-plus-1',
        'usd-a-plus-1',
        // PROFESSIONAL
        'aud-m-professional-1',
        'aud-a-professional-1',
        'cad-m-professional-1',
        'cad-a-professional-1',
        'eur-m-professional-1',
        'eur-a-professional-1',
        'gbp-m-professional-1',
        'gbp-a-professional-1',
        'hkd-m-professional-1',
        'hkd-a-professional-1',
        'usd-m-professional-1',
        'usd-q-professional-1',
        'usd-a-professional-1',
        // ENTERPRISE
        'aud-m-enterprise-1',
        'aud-a-enterprise-1',
        'cad-m-enterprise-1',
        'cad-a-enterprise-1',
        'eur-m-enterprise-1',
        'eur-a-enterprise-1',
        'gbp-m-enterprise-1',
        'gbp-a-enterprise-1',
        'hkd-m-enterprise-1',
        'hkd-a-enterprise-1',
        'usd-m-enterprise-1',
        'usd-a-enterprise-1',
        // GROWTH
        'aud-m-growth-2',
        'aud-a-growth-2',
        'cad-m-growth-2',
        'cad-a-growth-2',
        'eur-m-growth-2',
        'eur-a-growth-2',
        'gbp-m-growth-2',
        'gbp-a-growth-2',
        'hkd-m-growth-2',
        'hkd-a-growth-2',
        'usd-m-growth-2',
        'usd-a-growth-2',
        // PREMIUM V2
        'aud-a-premium-2',
        'aud-m-premium-2',
        'cad-a-premium-2',
        'cad-m-premium-2',
        'eur-a-premium-2',
        'eur-m-premium-2',
        'gbp-a-premium-2',
        'gbp-m-premium-2',
        'hkd-a-premium-2',
        'hkd-m-premium-2',
        'usd-a-premium-2',
        'usd-m-premium-2',
        'nzd-a-premium-2',
        'nzd-m-premium-2',
        'sgd-a-premium-2',
        'sgd-m-premium-2',
        // PROFESSIONAL V2
        'aud-a-professional-2',
        'cad-a-professional-2',
        'eur-a-professional-2',
        'gbp-a-professional-2',
        'hkd-a-professional-2',
        'usd-a-professional-2',
        'nzd-a-professional-2',
        'sgd-a-professional-2',
        // ENTERPRISE V2
        'aud-a-enterprise-2',
        'cad-a-enterprise-2',
        'eur-a-enterprise-2',
        'gbp-a-enterprise-2',
        'hkd-a-enterprise-2',
        'usd-a-enterprise-2',
        'nzd-a-enterprise-2',
        'sgd-a-enterprise-2',
        // GG Enterprise V2
        'aud-a-ggenterprise-2',
        'aud-m-ggenterprise-2',
        'cad-a-ggenterprise-2',
        'cad-m-ggenterprise-2',
        'eur-a-ggenterprise-2',
        'eur-m-ggenterprise-2',
        'gbp-a-ggenterprise-2',
        'gbp-m-ggenterprise-2',
        'hkd-a-ggenterprise-2',
        'hkd-m-ggenterprise-2',
        'usd-a-ggenterprise-2',
        'usd-m-ggenterprise-2',
        'nzd-a-ggenterprise-2',
        'nzd-m-ggenterprise-2',
        'sgd-a-ggenterprise-2',
        'sgd-m-ggenterprise-2',
    ],
    'available' => [
        // CURRENT PLANS
        // AWARDS
        // GROWTH
        'aud-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'nzd-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'sgd-a-growth-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        // PROFESSIONAL
        'aud-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'nzd-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'sgd-a-professional-3' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        //PROPARTNER
        'aud-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'cad-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'eur-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'gbp-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'hkd-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'usd-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'nzd-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'sgd-a-propartner-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],

        // ENTERPRISE
        'aud-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'nzd-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'sgd-a-premier-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        // GRANDFATHERED
        // STARTER V2
        'aud-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'nzd-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'sgd-a-starter-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        // STARTER V1
        'aud-a-starter-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-starter-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-starter-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-starter-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-starter-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-starter-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-m-starter-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        // PLUS V2
        'aud-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'nzd-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'sgd-a-plus-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        // PLUS V1
        'aud-a-plus-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-plus-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-plus-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-plus-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-plus-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-plus-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-m-plus-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        //PROFESSIONAL V1
        'aud-a-professional-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-professional-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-professional-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-professional-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-professional-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-professional-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        // PROFESSIONAL V2
        'aud-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'nzd-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'sgd-a-professional-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        //PRO PUBLISHER V1
        'aud-a-propublisher-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-propublisher-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-propublisher-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-propublisher-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-propublisher-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-propublisher-1' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        // PROPUBLISHER 2
        'aud-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'cad-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'eur-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'gbp-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'hkd-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'usd-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'nzd-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        'sgd-a-propublisher-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],

        ],
        // ENTERPRISE
        'aud-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'cad-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'eur-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'gbp-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'hkd-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'usd-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'nzd-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        'sgd-a-enterprise-2' => [
            'brand' => 'awardforce',
            'verticals' => [
                'awards',
            ],
        ],
        //GOODGRANTS
        // INTRO
        'aud-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'aud-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-a-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-m-intro-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        // PREMIUM
        'aud-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'aud-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-a-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-m-premium-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        // GG Enterprise
        'aud-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'aud-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-a-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-m-ggenterprise-3' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        // GRANDFATHERED
        // PREMIUM
        'aud-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'aud-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-a-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-m-premium-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        // GG Enterprise
        'aud-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'aud-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'cad-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'eur-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'gbp-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'hkd-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'usd-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'nzd-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-a-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
        'sgd-m-ggenterprise-2' => [
            'brand' => 'goodgrants',
            'verticals' => [
                'grants',
            ],
        ],
    ],
];
