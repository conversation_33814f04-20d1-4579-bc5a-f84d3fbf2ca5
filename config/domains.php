<?php
/**
 * =======================================
 * ===== IMPORTANT - PLATFORM CONFIG =====
 * =======================================
 *
 * Please note, this config file is provided by platform!
 * Ensure all changes that apply to platform are made there.
 * Be careful publishing and merging updates from platform.
 *
 * The domains configuration highlights all root domains supported by Award Force,
 * as well as reserved subdomains that cannot be used when setting up or updating
 * accounts.
 */

return [
    // Root domains that are supported by the platform
    'white_label' => array_merge(
        explode(',', env('WHITE_LABEL_DOMAINS', '')),
        explode(',', env('WHITE_LABEL_DOMAINS_AWARDFORCE', '')),
        explode(',', env('WHITE_LABEL_DOMAINS_GOODGRANTS', '')),
    ),

    'brands' => [
        'awardforce' => explode(',', env('WHITE_LABEL_DOMAINS_AWARDFORCE', '')),
        'goodgrants' => explode(',', env('WHITE_LABEL_DOMAINS_GOODGRANTS', '')),
    ],

    // Domain that assets will be served from
    'assets' => env('ASSETS_DOMAIN'),

    // CNAME to point custom domains towards
    'custom' => env('CUSTOM_DOMAINS_CNAME'),

    // Reserved subdomains
    'reserved' => [
        '_domainkey',
        'admin',
        'api',
        'assets',
        'auth',
        'cr4ce',
        'email',
        'falcon',
        'falcon-staging',
        'falcon-uat',
        'ftp',
        'mail',
        'maintenance',
        'manage',
        'mg',
        'pop',
        'provisioning',
        'secure',
        'ses',
        'smtp',
        'ssh',
        'support',
        'transcoding',
        'uat',
        'www',
        'wwww',
    ],

    'setup' => [
        'awardforce' => env('AWARDFORCE_DOMAIN_SETUP'),
        'goodgrants' => env('GOODGRANTS_DOMAIN_SETUP'),
    ],
];
