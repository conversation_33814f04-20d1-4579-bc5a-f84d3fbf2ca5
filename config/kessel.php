<?php
/**
 * =======================================
 * ===== IMPORTANT - PLATFORM CONFIG =====
 * =======================================
 *
 * Please note, this config file is provided by platform!
 * Ensure all changes that apply to platform are made there.
 * Be careful publishing and merging updates from platform.
 */

return [
    // Endpoint of matching Kessel API this app communicates with, i.e. af4.kessel.tectonic, one for
    // each service that we support. The default endpoint is the one that is mostly used by the service, but the others
    // can be switched to on an as-needed basis.
    'endpoints' => collect(explode(',', env('AF_REGIONS', '')))->mapWithKeys(function ($region) {
        $uppercaseRegion = strtoupper($region);

        return [
            "af_{$region}" => env("KESSEL_ENDPOINT_AF_{$uppercaseRegion}"),
            "af_{$region}_uat" => env("KESSEL_ENDPOINT_AF_{$uppercaseRegion}_UAT"),
        ];
    })->merge([
        'my_af' => env('KESSEL_ENDPOINT_MY_AF'),
        'billing_portal' => env('KESSEL_ENDPOINT_BILLING_PORTAL'),
    ])->all(),

    // Private Key
    'key' => env('KESSEL_KEY'),

    // Domain name this app is accessed via. i.e. app.kessel.tectonic
    'domain' => env('KESSEL_DOMAIN'),

    // Authorisation Manager class implementation
    'manager' => 'consumer.manager',

    // System consumer implementation (for non-user signatures)
    'system_consumer' => 'consumer.system',

    'origin' => env('KESSEL_ORIGIN'),
];
