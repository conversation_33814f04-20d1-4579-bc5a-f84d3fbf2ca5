<?php

namespace AwardForce\Modules\Ecommerce\Cart\Middleware;

use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Accounts\Contracts\SupportedCurrencyRepository;
use AwardForce\Modules\Accounts\Services\CurrentAccountService;
use AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\EntryPriceVariants\DefaultVariant;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Payments\Repositories\PriceRepository;
use Illuminate\Support\Collection;
use Platform\Http\Messaging;

class PaymentConfigurationCheck
{
    use Messaging;

    /**
     * @var SupportedCurrencyRepository
     */
    private $supportedCurrencies;

    /**
     * @var PriceRepository
     */
    private $prices;

    /**
     * @var CurrentAccountService
     */
    private $currentAccount;

    public function __construct(
        SupportedCurrencyRepository $supportedCurrencies,
        PriceRepository $prices,
        CurrentAccountService $currentAccount
    ) {
        $this->supportedCurrencies = $supportedCurrencies;
        $this->prices = $prices;
        $this->currentAccount = $currentAccount;
    }

    public function handle($request, \Closure $next)
    {
        if (feature_disabled('order_payments')) {
            return $next($request);
        }

        if (! $this->supportedCurrencies->getDefault()) {
            return $this->withMessage(trans('payments.sanity.no_default_currency'), 'warning');
        }

        $prices = $this->prices->getForSeason($this->currentAccount->activeSeasonId());

        if ($prices->isEmpty()) {
            return $this->withMessage(trans('payments.sanity.no_prices_for_season'), 'warning');
        }

        $supportedCurrencies = $this->supportedCurrencies->getAll();

        foreach ($prices as $price) {
            $amounts = $price->getAmounts();

            if ($amounts->isEmpty() || ! $this->defaultAmountsExist($amounts, $supportedCurrencies)) {
                return $this->withMessage(trans('payments.sanity.no_default_amounts'), 'warning');
            }
        }

        return $next($request);
    }

    private function defaultAmountsExist(Collection $amounts, Collection $supportedCurrencies): bool
    {
        $defaultAmounts = $amounts->filter(function ($amount) {
            return (new DefaultVariant)->appliesTo($amount, new Entry);
        });

        $amountsForCurrency = $defaultAmounts->reduce(function ($carry, $defaultAmount) use ($supportedCurrencies) {
            $supportedCurrencies->each(function ($supportedCurrency) use (&$carry, $defaultAmount) {
                if ($amount = $defaultAmount->amountForCurrency(new Currency($supportedCurrency->code))) {
                    $carry->put($supportedCurrency->code, $amount);
                }
            });

            return $carry;
        }, new Collection);

        return $amountsForCurrency->count() === $supportedCurrencies->count();
    }
}
