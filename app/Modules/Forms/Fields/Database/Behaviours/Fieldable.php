<?php

namespace AwardForce\Modules\Forms\Fields\Database\Behaviours;

use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use Platform\Search\HasValues;

trait Fieldable
{
    private $rawFields;

    private function valuesService(): ValuesService
    {
        return app(ValuesService::class);
    }

    protected function defaultProvider(): ?HasValues
    {
        return $this;
    }

    /**
     * Returns true if the value for the field <b>$field</b> exists and is not empty.
     */
    public function hasFieldValue(?Field $field): bool
    {
        return ! is_null($field) && ! empty($this->values[(string) $field->slug] ?? '');
    }

    /**
     * Retrieve fields with values for a given entity
     * THIS IS NOT A RELATION ANYMORE!
     */
    public function getFieldsAttribute(): Fields
    {
        return $this->rawFields ?? once(fn() => $this->valuesService()->mapValuesToFields($this->defaultProvider())->sortBy('order'));
    }

    /**
     * Set fields for object without persisting them
     * ( for translation purposes )
     */
    public function setFieldsAttribute(Fields $fields): void
    {
        $this->rawFields = $fields;
    }

    /**
     * Returns all the field values for a given entity as an assoc:
     * [
     *      fieldSlug => fieldValue
     *      ...
     * ]
     */
    public function getFieldValuesAttribute(): array
    {
        return $this->fields->pluck('value', 'slug')->toArray();
    }

    /**
     * Saves (overwrites) values for given entity from assoc:
     * [
     *      fieldSlug => fieldValue
     *      ...
     * ]
     *
     *
     * @throws \AwardForce\Modules\Forms\Fields\Exceptions\CannotModifyFieldValue
     */
    public function setFieldValuesAttribute(array $values)
    {
        $this->valuesService()->setValuesForObject($values, $this->defaultProvider())->refresh();
    }
}
