const mix = require('laravel-mix');
const exec = require('child_process').exec;
const path = require('path');
const dotenv = require('dotenv');
const { styles } = require('@ckeditor/ckeditor5-dev-utils');
const CopyWebpackPlugin = require('copy-webpack-plugin');

const CKERegex = {
	svg: /ckeditor5-[^/\\]+[/\\]theme[/\\]icons[/\\][^/\\]+\.svg$/,
	css: /ckeditor5-[^/\\]+[/\\]theme[/\\].+\.css$/,
};

const ResolveTypeScriptPlugin = require('resolve-typescript-plugin').default;
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');

Mix.listen('configReady', (webpackConfig) => {
	const targetSVG = /(\.(png|jpe?g|gif|webp|avif)$|^((?!font).)*\.svg$)/;
	const targetFont = /(\.(woff2?|ttf|eot|otf)$|font.*\.svg$)/;
	const targetCSS = /\.p?css$/;

	for (let rule of webpackConfig.module.rules) {
		if (rule.test.toString() === targetSVG.toString()) {
			rule.exclude = CKERegex.svg;
		} else if (rule.test.toString() === targetFont.toString()) {
			rule.exclude = CKERegex.svg;
		} else if (rule.test.toString() === targetCSS.toString()) {
			rule.exclude = CKERegex.css;
		}
	}
});

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for your application, as well as bundling up your JS files.
 |
 */

const inProduction = mix.inProduction();
const cssExtension = inProduction ? '.min.css' : '.dev.css';
const jsExtension = inProduction ? '.min.js' : '.dev.js';

const sassConfig = {
	implementation: require('sass'),
};

dotenv.config();

mix.options({
	processCssUrls: false,
});

mix.webpackConfig({
	output: {
		chunkFilename: 'js/chunks/chunk-[contenthash].js',
	},
	resolve: {
		alias: {
			simplemde$: 'simplemde/src/js/simplemde.js',
			pusher$: 'pusher-js/dist/pusher.js',
			vue$: 'vue/dist/vue.common.js',

			'@': path.resolve(__dirname, 'resources/assets/js/src'),
			'@tests': path.resolve(__dirname, 'resources/assets/js/tests'),
			'@scss': path.resolve(__dirname, 'resources/assets/sass'),
			autosaver: path.resolve(__dirname, 'resources/assets/js/src/lib/autosaver.js'),
			filesize: path.resolve(__dirname, 'resources/assets/js/src/vendor/filesize/filesize.js'),
			plupload: path.resolve(__dirname, 'resources/assets/js/src/vendor/plupload/plupload.full.min.js'),
			repeater: path.resolve(__dirname, 'resources/assets/js/src/lib/repeater.js'),
			spectrum: path.resolve(__dirname, 'resources/assets/js/src/vendor/spectrum/spectrum.js'),
			tabular: path.resolve(__dirname, 'resources/assets/js/src/lib/tabular/tabs.js'),
			tectoastr: path.resolve(__dirname, 'resources/assets/js/src/lib/tectoastr.js'),
			toastr: path.resolve(__dirname, 'resources/assets/js/src/vendor/toastr/toastr.js'),
			underscore: path.resolve(__dirname, 'resources/assets/js/src/vendor/underscore/underscore.js'),
			uploader: path.resolve(__dirname, 'resources/assets/js/src/lib/uploader.js'),

			// https://github.com/desandro/imagesloaded/issues/203
			'matches-selector/matches-selector': 'desandro-matches-selector',
			'eventEmitter/EventEmitter': 'wolfy87-eventemitter',
			'get-style-property/get-style-property': 'desandro-get-style-property',
			signature_pad: 'signature_pad/dist/signature_pad',
		},
		extensions: ['.js', '.vue', '.ts'],
		fullySpecified: false,
		plugins: [new ResolveTypeScriptPlugin()],
	},
	module: {
		rules: [
			{
				test: /\.js$/,
				exclude: /node_modules/,
				loader: 'babel-loader',
				options: {
					presets: ['@babel/preset-env'],
				},
			},
			{
				test: /\.tsx?$/,
				loader: 'ts-loader',
				options: {
					appendTsSuffixTo: [/\.vue$/],
					transpileOnly: true,
				},
				exclude: /node_modules/,
			},
			// Start of CKEditor 5 css
			{
				test: CKERegex.svg,

				use: ['raw-loader'],
			},
			{
				test: CKERegex.css,

				use: [
					{
						loader: 'style-loader',
						options: {
							injectType: 'singletonStyleTag',
							attributes: {
								'data-cke': true,
							},
						},
					},
					'css-loader',
					{
						loader: 'postcss-loader',
						options: {
							postcssOptions: styles.getPostCssConfig({
								themeImporter: {
									themePath: require.resolve('@ckeditor/ckeditor5-theme-lark'),
								},
								minify: true,
							}),
						},
					},
				],
			},
			// End of CKEditor 5 css
		],
	},
	plugins: [
		new ForkTsCheckerWebpackPlugin(),
		new CopyWebpackPlugin({
			patterns: [
				{
					from: path.join(path.dirname(require.resolve('pdfjs-dist/package.json')), 'build', 'pdf.worker.min.mjs'),
					to: 'pdf.worker.min.js',
				},
			],
		}),
	],
	devtool: 'source-map',
	externals: {
		jquery: 'jQuery',
		jQuery: 'jQuery',
		$: 'jQuery',
	},
	stats: {
		children: false,
	},
});

const buildStyles = function (brand) {
	mix
		.sass(`resources/assets/sass/${brand}.scss`, `public/css/${brand}${cssExtension}`, sassConfig)
		.sass(`resources/assets/sass/${brand}-rtl.scss`, `public/css/${brand}-rtl${cssExtension}`, sassConfig)
		.sass(`resources/assets/sass/${brand}-splash.scss`, `public/css/${brand}-splash${cssExtension}`, sassConfig)
		.sass(`resources/assets/sass/${brand}-rtl-splash.scss`, `public/css/${brand}-rtl-splash${cssExtension}`, sassConfig)
		.then(() => {
			exec(
				`node_modules/rtlcss/bin/rtlcss.js public/css/${brand}-rtl${cssExtension} ./public/css/${brand}-rtl${cssExtension}`

			);exec(
				`node_modules/rtlcss/bin/rtlcss.js public/css/${brand}-rtl-splash${cssExtension} ./public/css/${brand}-rtl-splash${cssExtension}`
			);
		});
	if (!process.env.NPM_SKIP_SOURCEMAPS) {
		mix.sourceMaps();
	}
};

// Build styles
buildStyles('awardforce');
buildStyles('goodgrants');

// Build scripts
mix.js('resources/assets/js/src/main.js', 'public/js/awardforce' + jsExtension).vue({ version: 2 });

if (!process.env.NPM_SKIP_SOURCEMAPS) {
	mix.sourceMaps(true, 'source-map');
}
