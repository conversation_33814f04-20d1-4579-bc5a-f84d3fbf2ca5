<?php $type = $multiselect->subset()->type(); ?>
<multiselect-option inline-template :selected="@js($multiselect->selected())">
    <div class="checkbox radio multiselect-option">
        <div class="multiselect-subset-control">
            {{ html()->$type($multiselect->subset()->name(), $id, (string) in_array($id, $multiselect->subsetSelected()))->attributes(['class' => in_array($id, $multiselect->selected()) ? '' : 'hidden', ':class' => '{ \'hidden\': !checked }']) }}
        </div>
        @include('platform::html.form.multiselect.option')
    </div>
</multiselect-option>
