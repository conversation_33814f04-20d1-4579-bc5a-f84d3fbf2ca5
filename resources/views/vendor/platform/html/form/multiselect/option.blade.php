<label>
    <input type="checkbox" name="{{ $multiselect->name() }}[]" class="conditional-ignore primary-option{{ is_string($value) ? ' unassigned' : '' }}" value="{{ $id }}"{!! $multiselect->optionSelected($id) ? ' checked="checked"' : '' !!} v-model="checked">
    @if (!is_object($value))
        <bdi>{{ (string) $value }}</bdi>
    @else
        {{ $value->value }}
        <span class='af-icons af-icons-info hover-tooltip' data-content='@lang('Multiselect'): {{ $value->extra }}'></span>
    @endif
</label>
