@include('entry.common.archived-icon', ['entry' => $entry])

@if($badges)
    <div class="labels">
        @foreach($badges as $badge)
            {!! HTML::badge($badge) !!}
        @endforeach
    </div>
@endif

<div class="submittable-title">
    {!! HTML::resourceLink($entry->title, route($entry->isArchived() ? 'entry.entrant.preview' : 'entry.entrant.edit', [$entry->slug]), $entry) !!}
    {{ HTML::collaboratorsCount($entry->collaboratorsCount) }}
</div>

@if($entry->hasDeadline())
    <div>
        @lang('entries.deadline.my_submission', ['deadline' => HTML::localisedDateTime($entry->deadline, Consumer::dateLocale())])
    </div>
@endif

<ul class="entry-title-links">
    @include('award.partials.entry-links')
    @include('payment.partials.entry-links')

    @if ($entry->displayPackingSlip())
        @include('entry.common.packing-slip-button')
    @endif

    @if ($entry->feedback)
        <li>@include('entry.manager.search.feedback', ['entry' => $entry])</li>
    @endif

    @if (feature_enabled('contracts'))
        @include('entry.common.contract-button', ['entry' => $entry])
    @endif
</ul>
