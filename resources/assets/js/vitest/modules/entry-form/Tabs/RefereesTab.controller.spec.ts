import { ref } from 'vue';
import store from '@/lib/store';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { beforeEach, Mock, vi } from 'vitest';
import { refereesTabController, View } from '@/modules/entry-form/Tabs/RefereesTab.controller';

vi.mock('@/lib/utils', () => ({
	getGlobal: vi.fn(),
	getGlobalData: vi.fn(),
}));

vi.mock('@/modules/entry-form/Configuration/handle-input', () => ({
	handleInput: vi.fn(),
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

const collaborationService = {
	service: {
		set: vi.fn(),
		subscribe: vi.fn(),
	},
	api: {
		createReferee: vi.fn().mockReturnValue(Promise.resolve({})),
	},
};
const addReferee = vi.fn();
vi.mock('@/modules/entry-form/Tabs/RefereesTabService', () => ({
	refereesTabService: () => ({
		collaborationService: () => collaborationService,
		eligibleReadOnly: () => () => true,
		myAccess: () => new Promise(() => {}),
		refereesByTab: () => ref([{ id: 1 }, { id: 2 }, { id: 3 }]),
		refereeFields: () => () => [],
		hasRefereeReviewStages: () => () => true,
		addReferee,
	}),
}));

vi.mock('@/lib/store', () => ({
	default: {
		state: {
			entryFormConfiguration: {
				configurationMode: false,
			},
			entryForm: {
				refereeReviewStages: [],
			},
		},
		getters: {
			'entryForm/referees': () => [{}, {}, {}],
			'entryForm/refereeFields': () => [],
			'entryForm/tabEligibilityReadOnly': () => false,
			'entryForm/refereesByTab': () => [{}, {}, {}],
		},
		dispatch: vi.fn().mockReturnValue(Promise.resolve([])),
	},
}));

const props = {
	tab: {
		id: 1,
		maxReferees: 2,
		minReferees: 1,
	},
};

const submittable = {
	isCollaborative: () => true,
	isGrantReport: () => false,
	getSubmittable: () => ({ slug: 'submittable-slug' }),
	getType: () => 'entrant',
	getForm: () => ({ slug: 'form-slug' }),
	isUpdatable: () => true,
};

describe('Referees Tab Controller', () => {
	beforeEach(() => {
		addReferee.mockReset();
	});

	it('it returns the group name with the id', () => {
		const controller = refereesTabController(props) as View;

		expect(controller.groupName('1')).toBe('referee-1');
		expect(controller.groupName(2)).toBe('referee-2');
	});

	it('returns the resource name', () => {
		const controller = refereesTabController(props) as View;

		expect(controller.resourceName).toBe('Referees');
	});

	it('referee limit reached', () => {
		const controller = refereesTabController(props) as View;
		expect(controller.refereeLimitReached()).toBe(true);

		const controllerFalse = refereesTabController({ ...props, tab: { ...props.tab, maxReferees: 10 } }) as View;
		expect(controllerFalse.refereeLimitReached()).toBe(false);
	});

	it('it should call loadReferees', () => {
		refereesTabController(props) as View;

		expect(store.dispatch).toHaveBeenCalledWith('entryForm/loadReferees', 1);
	});

	it('it should call collaborationService.service.set in loadReferees', () => {
		refereesTabController(props) as View;
		expect(collaborationService.service.set).toHaveBeenCalled();
	});

	it('it should call createReferee api if isCollaborative is true and addReferee was called', () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				...submittable,
				isCollaborative: () => true,
			},
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		const controller = refereesTabController(props) as View;
		controller.addReferee();

		expect(collaborationService.api.createReferee).toHaveBeenCalled();
	});

	it('it should call collaborationService.service.set if isCollaborative is true and addReferee was called', () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				...submittable,
				isCollaborative: () => false,
			},
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		const controller = refereesTabController(props) as View;
		controller.addReferee();

		expect(collaborationService.service.set).toHaveBeenCalledWith([{ id: 1 }, { id: 2 }, { id: 3 }]);
	});

	it('it should call service.addReferee if isCollaborative is false and addReferee was called', () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				...submittable,
				isCollaborative: () => false,
			},
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		const controller = refereesTabController(props) as View;
		controller.addReferee();

		expect(addReferee).toHaveBeenCalledWith(1);
	});
});
