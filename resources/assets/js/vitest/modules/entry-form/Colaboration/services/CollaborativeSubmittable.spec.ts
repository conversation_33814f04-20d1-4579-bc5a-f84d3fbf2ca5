import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { useEntryFormApi } from '@/modules/entry-form/Collaboration/services/Api';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

vi.mock('@/modules/entry-form/Collaboration/services/Api', () => ({
	useEntryFormApi: vi.fn(),
	emptyApi: 'empty-api-reference',
}));

describe('CollaborativeSubmittable service', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		(useEntryFormContainer as Mock).mockReturnValue({});
	});

	it('returns non-collaborative / npn-updatable for non collaboarative submittable', () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				isCollaborative: () => false,
				isUpdatable: () => false,
			},
		});
		expect(useCollaborativeSubmittable()).toEqual({
			isCollaborative: false,
			isUpdatable: false,
			api: 'empty-api-reference',
			owner: null,
			refreshCollaborators: expect.any(Function),
		});
	});

	it('returns collaborative submittable data', () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				isCollaborative: () => true,
				isUpdatable: () => true,
				getSubmittable: () => ({ slug: 'submittable-slug' }),
				getForm: () => ({ slug: 'form-slug' }),
				getType: () => 'entrant',
				isGrantReport: () => false,
			},
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		(useEntryFormApi as Mock).mockReturnValue('this is api');

		expect(useCollaborativeSubmittable()).toEqual({
			submittableSlug: 'submittable-slug',
			formSlug: 'form-slug',
			isCollaborative: true,
			isUpdatable: true,
			api: 'this is api',
			consumer: 'consumer-data',
		});

		expect(useEntryFormApi).toHaveBeenCalledWith('submittable-slug', 'entrant');
	});

	it('returns updatable submittable data', () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				isCollaborative: () => false,
				isUpdatable: () => true,
				getSubmittable: () => ({ slug: 'submittable-slug' }),
				getForm: () => ({ slug: 'form-slug' }),
				getType: () => 'entrant',
				isGrantReport: () => false,
			},
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		(useEntryFormApi as Mock).mockReturnValue('this is api');

		expect(useCollaborativeSubmittable()).toEqual({
			submittableSlug: 'submittable-slug',
			formSlug: 'form-slug',
			isCollaborative: false,
			isUpdatable: true,
			api: 'this is api',
			consumer: 'consumer-data',
			owner: null,
		});

		expect(useEntryFormApi).toHaveBeenCalledWith('submittable-slug', 'entrant');
	});
});
