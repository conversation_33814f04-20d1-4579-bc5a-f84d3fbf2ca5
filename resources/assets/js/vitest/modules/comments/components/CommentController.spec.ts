import { commentControllerFactory } from '@/lib/components/Comments/Comment.controller';
import { CommentEvents } from '@/lib/components/Comments/Comment.events';
import Vue from 'vue';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { Comment, CommentMode, CommentProps, CommentView } from '@/lib/components/Comments/Comment.types';

vi.mock('underscore', () => ({
	underscore: vi.fn(),
}));

vi.mock('tectoastr', () => ({
	tectoastr: vi.fn((foo = null) => foo),
}));

vi.mock('toastr', () => ({
	toastr: vi.fn((foo = null) => foo),
}));

vi.mock('@/lib/utils', () => ({
	getGlobalData: (key: string) => {
		switch (key) {
			case 'translations':
				return {
					'en_GB.test': {
						a: {
							b: 'val-test-a-b',
							c: 'val-test-a-c',
						},
					},
				};
			case 'language':
				return {
					locale: 'en_GB',
					fallback: 'en_GB',
				};
		}
	},
	getGlobal: (key: string) => key,
	useGlobal: (key: string) => key,
}));

vi.mock('@/lib/store', () => ({
	default: {
		getters: { 'validation/hasMultilingualError': false },
		state: {
			global: {
				preferredLanguage: 'en_GB',
				supportedLanguages: ['en_GB', 'it_IT', 'nl_NL'],
			},
			validation: {
				validationErrors: {},
			},
			comments: {
				state: {
					pendingUploads: 2,
					userId: 99,
				},
			},
		},
	},
}));

vi.mock('@/lib/components/Comments/commentsService', () => ({
	commentsServiceFactory: () => ({
		pendingUploads: () => 0,
		changePendingUploadsBy: () => {},
		userId: () => 88,
		commentsEdited: () => 0,
		changeCommentsEditedBy: () => {},
	}),
}));

vi.mock('@/lib/markdown/html-to-markdown', () => ({
	htmlToMarkdown: () => 'mockedCommentContent',
}));

const comment: Comment = {
	id: 9,
	slug: 'aCoMmEnT',
	name: 'A comment',
	content: 'A comment content',
	userId: 77,
	temp: false,
	files: [],
};

const props = {
	comment: comment,
	token: '',
	readOnly: false,
	deleteUrl: 'delete/a/comment/',
	updateUrl: 'update/a/comment/',
	translations: {},
} as CommentProps;

const emit = vi.fn();

describe('CommentController', () => {
	afterEach(() => {
		vi.clearAllMocks();
		Vue.prototype.$http = {};
	});

	it('startedEditing and cancelEditing change the mode', () => {
		const controller: CommentView = commentControllerFactory()(props, emit);

		expect(controller.mode.value).toBe(CommentMode.VIEW);

		controller.startedEditing();

		expect(controller.mode.value).toBe(CommentMode.EDIT);
		expect(controller.commentContent.value).toBe('mockedCommentContent');

		controller.cancelEditing();

		expect(controller.mode.value).toBe(CommentMode.VIEW);
	});

	it('finishedEditing emits updated event', async () => {
		const commentUpdatedResponse = {
			content: 'co',
			files: [],
			relativeTime: 'relTime',
			slug: 'sLuG',
			user: 'Author Name',
			userId: 77,
		};

		Vue.prototype.$http = {
			put: () => Promise.resolve({ data: commentUpdatedResponse }),
		};

		const controller: CommentView = commentControllerFactory()(props, emit);
		controller.startedEditing();

		expect(controller.mode.value).toBe(CommentMode.EDIT);

		await controller.finishedEditing();

		expect(controller.mode.value).toBe(CommentMode.VIEW);
		expect(emit).toHaveBeenCalledWith(CommentEvents.Updated, commentUpdatedResponse);
	});

	it('deleteComment emits deleted event', async () => {
		Vue.prototype.$http = {
			post: () => Promise.resolve({}),
		};

		const controller: CommentView = commentControllerFactory()(props, emit);
		await controller.deleteComment();

		expect(emit).toHaveBeenCalledWith(CommentEvents.Deleted);
	});

	it('adds deleted file ID in onDeletedFile', async () => {
		const controller: CommentView = commentControllerFactory()(props, emit);

		const deletedFile = {
			slug: 'aFiLe',
			id: 321,
		};
		await controller.onDeletedFile(deletedFile);

		expect(controller.deletingFiles.value).toEqual([321]);
	});
});
