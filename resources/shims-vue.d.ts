declare module '*.vue';
declare module 'jquery';
declare module 'lodash';
declare module 'plupload';
declare module 'portal-vue';
declare module 'tectoastr';
declare module 'toastr';
declare module 'vue-bootstrap';
declare module 'vue-outside-events';
declare module 'object-hash';

declare module '@ckeditor/ckeditor5-alignment/src/alignment';
declare module '@ckeditor/ckeditor5-autoformat/src/autoformat';
declare module '@ckeditor/ckeditor5-basic-styles/src/bold';
declare module '@ckeditor/ckeditor5-basic-styles/src/code';
declare module '@ckeditor/ckeditor5-basic-styles/src/italic';
declare module '@ckeditor/ckeditor5-basic-styles/src/strikethrough';
declare module '@ckeditor/ckeditor5-basic-styles/src/subscript';
declare module '@ckeditor/ckeditor5-basic-styles/src/superscript';
declare module '@ckeditor/ckeditor5-basic-styles/src/underline';
declare module '@ckeditor/ckeditor5-block-quote/src/blockquote';
declare module '@ckeditor/ckeditor5-essentials/src/essentials';
declare module '@ckeditor/ckeditor5-heading/src/heading';
declare module '@ckeditor/ckeditor5-image/src/image';
declare module '@ckeditor/ckeditor5-image/src/imagecaption';
declare module '@ckeditor/ckeditor5-image/src/imageresize/imageresizehandles';
declare module '@ckeditor/ckeditor5-image/src/imageresize';
declare module '@ckeditor/ckeditor5-image/src/imagestyle';
declare module '@ckeditor/ckeditor5-indent/src/indent';
declare module '@ckeditor/ckeditor5-indent/src/indentblock';
declare module '@ckeditor/ckeditor5-link/src/link';
declare module '@ckeditor/ckeditor5-link/src/linkimage';
declare module '@ckeditor/ckeditor5-list/src/list';
declare module '@ckeditor/ckeditor5-media-embed/src/mediaembed';
declare module '@ckeditor/ckeditor5-paragraph/src/paragraph';
declare module '@ckeditor/ckeditor5-table/src/table';
declare module '@ckeditor/ckeditor5-table/src/tabletoolbar';
